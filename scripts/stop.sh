#!/bin/bash

# 宠物用品电商网站停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 停止服务
stop_services() {
    print_step "停止 Docker 服务..."
    
    # 检查是否使用新版 docker compose 命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    # 停止所有服务
    $COMPOSE_CMD down
    
    print_message "所有服务已停止"
}

# 主函数
main() {
    echo "🐾 宠物用品电商网站 - 停止服务"
    echo "================================"
    
    stop_services
    
    print_message "服务停止完成！"
}

# 执行主函数
main "$@"
