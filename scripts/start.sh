#!/bin/bash

# 宠物用品电商网站启动脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_dependencies() {
    print_step "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_message "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    print_step "创建必要的目录..."
    
    directories=(
        "wordpress/themes"
        "wordpress/plugins"
        "wordpress/uploads"
        "mysql/data"
        "redis/data"
        "nginx/ssl"
        "logs"
        "backups"
        "frontend"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_message "创建目录: $dir"
        fi
    done
}

# 复制环境变量文件
setup_env() {
    print_step "设置环境变量..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_message "已创建 .env 文件，请根据需要修改配置"
        else
            print_warning ".env.example 文件不存在"
        fi
    else
        print_message ".env 文件已存在"
    fi
}

# 设置文件权限
set_permissions() {
    print_step "设置文件权限..."
    
    # WordPress 目录权限
    if [ -d "wordpress" ]; then
        chmod -R 755 wordpress/
        print_message "设置 WordPress 目录权限"
    fi
    
    # 脚本执行权限
    chmod +x scripts/*.sh 2>/dev/null || true
    
    print_message "权限设置完成"
}

# 启动服务
start_services() {
    print_step "启动 Docker 服务..."
    
    # 检查是否使用新版 docker compose 命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    # 拉取最新镜像
    print_message "拉取 Docker 镜像..."
    $COMPOSE_CMD pull
    
    # 构建并启动服务
    print_message "启动服务..."
    $COMPOSE_CMD up -d
    
    print_message "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    print_step "等待服务就绪..."
    
    # 等待 MySQL 就绪
    print_message "等待 MySQL 数据库启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec pet-store-mysql mysqladmin ping -h localhost --silent; then
            print_message "MySQL 数据库已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "MySQL 数据库启动超时"
        exit 1
    fi
    
    # 等待 WordPress 就绪
    print_message "等待 WordPress 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:8080 > /dev/null; then
            print_message "WordPress 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "WordPress 启动超时"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    print_step "服务访问信息"
    
    echo ""
    echo "🎉 宠物用品电商网站开发环境已启动！"
    echo ""
    echo "📋 服务访问地址："
    echo "   WordPress 管理后台: http://localhost:8080/wp-admin"
    echo "   WordPress API:     http://localhost:8080/wp-json"
    echo "   phpMyAdmin:        http://localhost:8081"
    echo "   主站点:            http://localhost"
    echo ""
    echo "🔑 默认登录信息："
    echo "   数据库用户名: wordpress"
    echo "   数据库密码:   wordpress_password"
    echo "   数据库名:     pet_store_db"
    echo ""
    echo "📝 下一步操作："
    echo "   1. 访问 WordPress 管理后台完成初始化设置"
    echo "   2. 安装必要的插件 (WooCommerce, ACF, JWT Auth 等)"
    echo "   3. 配置宠物用品相关的自定义字段"
    echo "   4. 开始前端 Nuxt.js 开发"
    echo ""
    echo "🛠️  管理命令："
    echo "   停止服务: ./scripts/stop.sh"
    echo "   查看日志: ./scripts/logs.sh"
    echo "   重启服务: ./scripts/restart.sh"
    echo ""
}

# 主函数
main() {
    echo "🐾 宠物用品电商网站 - 开发环境启动脚本"
    echo "================================================"
    
    check_dependencies
    create_directories
    setup_env
    set_permissions
    start_services
    wait_for_services
    show_access_info
    
    print_message "启动完成！"
}

# 执行主函数
main "$@"
