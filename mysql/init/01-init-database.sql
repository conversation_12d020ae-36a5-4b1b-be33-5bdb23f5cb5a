-- 宠物用品电商数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS pet_store_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE pet_store_db;

-- 创建用户并授权（如果不存在）
CREATE USER IF NOT EXISTS 'wordpress'@'%' IDENTIFIED BY 'wordpress_password';
GRANT ALL PRIVILEGES ON pet_store_db.* TO 'wordpress'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建一些基础的自定义表（可选，主要用于扩展功能）

-- 宠物品种表
CREATE TABLE IF NOT EXISTS pet_breeds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category ENUM('dog', 'cat', 'bird', 'fish', 'small_animal', 'reptile') NOT NULL,
    description TEXT,
    care_level ENUM('easy', 'moderate', 'difficult') DEFAULT 'moderate',
    size ENUM('small', 'medium', 'large', 'extra_large') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_care_level (care_level),
    INDEX idx_size (size)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 产品评分表（扩展 WooCommerce 评分功能）
CREATE TABLE IF NOT EXISTS product_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title VARCHAR(200),
    review_content TEXT,
    verified_purchase BOOLEAN DEFAULT FALSE,
    helpful_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_id (product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    UNIQUE KEY unique_user_product (user_id, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 产品库存历史表
CREATE TABLE IF NOT EXISTS inventory_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL,
    change_type ENUM('in', 'out', 'adjustment') NOT NULL,
    quantity_change INT NOT NULL,
    previous_stock INT NOT NULL,
    new_stock INT NOT NULL,
    reason VARCHAR(255),
    user_id BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_product_id (product_id),
    INDEX idx_change_type (change_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户宠物信息表
CREATE TABLE IF NOT EXISTS user_pets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    pet_name VARCHAR(100) NOT NULL,
    pet_type ENUM('dog', 'cat', 'bird', 'fish', 'small_animal', 'reptile') NOT NULL,
    breed_id INT,
    age_years TINYINT,
    age_months TINYINT,
    weight DECIMAL(5,2),
    gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown',
    special_needs TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_pet_type (pet_type),
    FOREIGN KEY (breed_id) REFERENCES pet_breeds(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些基础的宠物品种数据
INSERT IGNORE INTO pet_breeds (name, category, description, care_level, size) VALUES
-- 狗狗品种
('金毛寻回犬', 'dog', '温和友善的大型犬，适合家庭饲养', 'moderate', 'large'),
('拉布拉多', 'dog', '聪明活泼的中大型犬，容易训练', 'moderate', 'large'),
('泰迪', 'dog', '小型卷毛犬，聪明可爱', 'easy', 'small'),
('柯基', 'dog', '短腿长身的中型犬，性格活泼', 'moderate', 'medium'),
('哈士奇', 'dog', '精力充沛的大型犬，需要大量运动', 'difficult', 'large'),

-- 猫咪品种
('英国短毛猫', 'cat', '温和安静的中型猫，适合公寓饲养', 'easy', 'medium'),
('美国短毛猫', 'cat', '健康强壮的中型猫，性格友善', 'easy', 'medium'),
('波斯猫', 'cat', '长毛优雅的猫咪，需要定期梳理', 'moderate', 'medium'),
('暹罗猫', 'cat', '活泼好动的猫咪，喜欢与人互动', 'moderate', 'medium'),
('布偶猫', 'cat', '温顺大型长毛猫，性格温和', 'moderate', 'large'),

-- 鸟类
('虎皮鹦鹉', 'bird', '小型鹦鹉，容易饲养和训练', 'easy', 'small'),
('玄凤鹦鹉', 'bird', '中型鹦鹉，聪明友善', 'moderate', 'medium'),
('金丝雀', 'bird', '小型鸣鸟，歌声优美', 'easy', 'small'),

-- 鱼类
('金鱼', 'fish', '经典观赏鱼，容易饲养', 'easy', 'small'),
('热带鱼', 'fish', '色彩丰富的观赏鱼', 'moderate', 'small'),

-- 小动物
('仓鼠', 'small_animal', '小型宠物，适合初学者', 'easy', 'small'),
('兔子', 'small_animal', '温和的中型宠物', 'moderate', 'medium'),
('龙猫', 'small_animal', '可爱的毛茸茸小动物', 'moderate', 'small');
