<?php
/**
 * 宠物用品电商网站 WordPress 自定义配置
 * 这个文件包含了针对 headless WordPress 的特殊配置
 */

// Redis 对象缓存配置
define('WP_REDIS_HOST', 'redis');
define('WP_REDIS_PORT', 6379);
define('WP_REDIS_PASSWORD', 'redis_password');
define('WP_REDIS_DATABASE', 0);
define('WP_REDIS_TIMEOUT', 1);
define('WP_REDIS_READ_TIMEOUT', 1);

// JWT 认证配置
define('JWT_AUTH_SECRET_KEY', 'your-secret-key-here-change-this-in-production');
define('JWT_AUTH_CORS_ENABLE', true);

// CORS 设置
define('CORS_ALLOW_ORIGIN', '*');
define('CORS_ALLOW_METHODS', 'GET, POST, PUT, DELETE, OPTIONS');
define('CORS_ALLOW_HEADERS', 'Content-Type, Authorization, X-Requested-With');

// WooCommerce 设置
define('WC_ADMIN_DISABLED', false); // 保留 WooCommerce 管理界面
define('WOOCOMMERCE_BLOCKS_PHASE', 3);

// 文件上传设置
define('WP_MEMORY_LIMIT', '512M');
define('MAX_EXECUTION_TIME', 300);

// 安全设置
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', false); // 允许插件安装
define('AUTOMATIC_UPDATER_DISABLED', true);

// 调试设置 (生产环境请关闭)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// 数据库优化
define('WP_POST_REVISIONS', 3);
define('AUTOSAVE_INTERVAL', 300);
define('WP_CRON_LOCK_TIMEOUT', 60);

// 媒体设置
define('UPLOADS', 'wp-content/uploads');
define('MEDIA_TRASH', true);

// API 设置
define('REST_API_ENABLED', true);
define('XMLRPC_ENABLED', false); // 禁用 XML-RPC

// 缓存设置
define('WP_CACHE', true);
define('COMPRESS_CSS', true);
define('COMPRESS_SCRIPTS', true);
define('CONCATENATE_SCRIPTS', false);
define('ENFORCE_GZIP', true);

// 多站点设置 (如果需要)
// define('WP_ALLOW_MULTISITE', true);

// 自定义表前缀 (已在 docker-compose.yml 中设置)
// $table_prefix = 'wp_';

// 语言设置
define('WPLANG', 'zh_CN');

// 时区设置
define('WP_TIMEZONE', 'Asia/Shanghai');

// 自动保存间隔
define('AUTOSAVE_INTERVAL', 300); // 5分钟

// 垃圾箱自动清理
define('EMPTY_TRASH_DAYS', 30);

// 图片质量设置
add_filter('jpeg_quality', function($quality) {
    return 85;
});

// 禁用不必要的功能
define('WP_POST_REVISIONS', 3);
define('DISALLOW_UNFILTERED_HTML', true);

// 自定义上传目录结构
define('UPLOADS_USE_YEARMONTH_FOLDERS', true);

// 启用 WordPress 维护模式 (需要时)
// define('WP_MAINTENANCE_MODE', true);

// 数据库字符集
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// 自定义错误页面
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// 限制登录尝试 (通过插件实现)
define('LIMIT_LOGIN_ATTEMPTS', true);

// 启用 WordPress 对象缓存
define('ENABLE_CACHE', true);

// 自定义 WordPress 目录
define('WP_CONTENT_DIR', ABSPATH . 'wp-content');
define('WP_CONTENT_URL', 'http://' . $_SERVER['HTTP_HOST'] . '/wp-content');

// 插件目录
define('WP_PLUGIN_DIR', WP_CONTENT_DIR . '/plugins');
define('WP_PLUGIN_URL', WP_CONTENT_URL . '/plugins');

// 主题目录
define('WP_THEME_DIR', WP_CONTENT_DIR . '/themes');

// 上传目录
define('UPLOAD_DIR', WP_CONTENT_DIR . '/uploads');

// 自定义 WordPress 钩子和过滤器
add_action('init', function() {
    // 移除不必要的 WordPress 头部信息
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // 启用 CORS
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
});

// 自定义 REST API 端点
add_action('rest_api_init', function() {
    // 注册自定义 API 端点
    register_rest_route('pet-store/v1', '/health', array(
        'methods' => 'GET',
        'callback' => function() {
            return array(
                'status' => 'ok',
                'timestamp' => current_time('mysql'),
                'version' => get_bloginfo('version')
            );
        },
        'permission_callback' => '__return_true'
    ));
});

// 优化数据库查询
add_action('init', function() {
    // 移除不必要的查询
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);
});

// 自定义媒体上传处理
add_filter('upload_mimes', function($mimes) {
    $mimes['svg'] = 'image/svg+xml';
    $mimes['webp'] = 'image/webp';
    return $mimes;
});

// 设置默认图片尺寸
add_action('after_setup_theme', function() {
    // 产品图片尺寸
    add_image_size('product-thumbnail', 300, 300, true);
    add_image_size('product-medium', 600, 600, true);
    add_image_size('product-large', 1200, 1200, true);
    
    // 宠物图片尺寸
    add_image_size('pet-avatar', 150, 150, true);
    add_image_size('pet-profile', 400, 400, true);
});

?>
