; PHP 配置文件 - 针对 WordPress 优化

; 内存限制
memory_limit = 512M

; 文件上传设置
upload_max_filesize = 64M
post_max_size = 64M
max_input_vars = 3000
max_execution_time = 300
max_input_time = 300

; 错误报告
display_errors = On
display_startup_errors = On
log_errors = On
error_log = /var/log/php_errors.log

; 时区设置
date.timezone = "Asia/Shanghai"

; OPcache 设置 (提高性能)
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

; Session 设置
session.save_handler = redis
session.save_path = "tcp://redis:6379?auth=redis_password"
session.gc_maxlifetime = 3600

; 安全设置
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off
