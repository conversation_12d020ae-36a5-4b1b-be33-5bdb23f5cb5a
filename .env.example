# 宠物用品电商网站环境变量配置文件
# 复制此文件为 .env 并修改相应的值

# 项目基本信息
PROJECT_NAME=pet-ecommerce-store
ENVIRONMENT=development
DOMAIN=localhost

# 数据库配置
MYSQL_ROOT_PASSWORD=root_password_change_this
MYSQL_DATABASE=pet_store_db
MYSQL_USER=wordpress
MYSQL_PASSWORD=wordpress_password_change_this

# WordPress 配置
WORDPRESS_DB_HOST=mysql:3306
WORDPRESS_DB_NAME=pet_store_db
WORDPRESS_DB_USER=wordpress
WORDPRESS_DB_PASSWORD=wordpress_password_change_this
WORDPRESS_TABLE_PREFIX=wp_
WORDPRESS_DEBUG=1

# JWT 认证密钥 (生产环境必须更改)
JWT_AUTH_SECRET_KEY=your-secret-key-here-change-this-in-production-environment

# Redis 配置
REDIS_PASSWORD=redis_password_change_this

# 端口配置
WORDPRESS_PORT=8080
MYSQL_PORT=3306
REDIS_PORT=6379
PHPMYADMIN_PORT=8081
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
FRONTEND_PORT=3000

# SSL 证书配置 (生产环境)
SSL_CERT_PATH=./nginx/ssl/cert.pem
SSL_KEY_PATH=./nginx/ssl/key.pem

# 邮件配置 (用于 WordPress 邮件发送)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_SECURE=tls

# 文件上传限制
MAX_UPLOAD_SIZE=64M
MAX_EXECUTION_TIME=300

# 缓存配置
ENABLE_REDIS_CACHE=true
CACHE_EXPIRATION=3600

# 安全配置
DISABLE_FILE_EDIT=true
DISABLE_XML_RPC=true
LIMIT_LOGIN_ATTEMPTS=true

# API 配置
CORS_ALLOW_ORIGIN=*
API_RATE_LIMIT=1000

# 支付网关配置 (WooCommerce)
# 支付宝
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key

# 微信支付
WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_wechat_mch_id
WECHAT_API_KEY=your_wechat_api_key

# PayPal (国际支付)
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# 物流配置
# 顺丰快递
SF_CUSTOMER_CODE=your_sf_customer_code
SF_CHECKWORD=your_sf_checkword

# 圆通快递
YTO_CUSTOMER_CODE=your_yto_customer_code
YTO_PASSWORD=your_yto_password

# 短信服务配置 (阿里云)
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
SMS_SIGN_NAME=your_sms_sign_name

# 对象存储配置 (阿里云 OSS)
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket_name
OSS_ENDPOINT=your_oss_endpoint

# CDN 配置
CDN_URL=https://your-cdn-domain.com

# 监控和日志
LOG_LEVEL=info
ENABLE_ERROR_REPORTING=true
SENTRY_DSN=your_sentry_dsn

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# 开发工具配置
XDEBUG_ENABLE=false
XDEBUG_REMOTE_HOST=host.docker.internal
XDEBUG_REMOTE_PORT=9003

# 前端配置 (Nuxt.js)
NUXT_PUBLIC_API_BASE=http://localhost:8080/wp-json
NUXT_PUBLIC_SITE_URL=http://localhost:3000
NUXT_SECRET_KEY=your-nuxt-secret-key

# 社交媒体集成
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
WECHAT_MINI_APP_ID=your_wechat_mini_app_id
WECHAT_MINI_APP_SECRET=your_wechat_mini_app_secret

# 搜索引擎优化
GOOGLE_ANALYTICS_ID=your_google_analytics_id
GOOGLE_SEARCH_CONSOLE_ID=your_google_search_console_id
BAIDU_ANALYTICS_ID=your_baidu_analytics_id

# 第三方服务
ELASTICSEARCH_URL=http://elasticsearch:9200
RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
