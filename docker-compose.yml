version: '3.8'

services:
  # WordPress 服务
  wordpress:
    image: wordpress:6.6-php8.3-apache
    container_name: pet-store-wordpress
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress_password
      WORDPRESS_DB_NAME: pet_store_db
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
        define('WP_MEMORY_LIMIT', '512M');
        define('JWT_AUTH_SECRET_KEY', 'your-secret-key-here-change-this-in-production');
        define('JWT_AUTH_CORS_ENABLE', true);
    volumes:
      - wordpress_data:/var/www/html
      - ./wordpress/themes:/var/www/html/wp-content/themes
      - ./wordpress/plugins:/var/www/html/wp-content/plugins
      - ./wordpress/uploads:/var/www/html/wp-content/uploads
      - ./wordpress/config/php.ini:/usr/local/etc/php/conf.d/custom.ini
    depends_on:
      - mysql
      - redis
    networks:
      - pet-store-network

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: pet_store_db
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress_password
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/config/my.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - pet-store-network

  # Redis 缓存服务
  redis:
    image: redis:7.2-alpine
    container_name: pet-store-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - pet-store-network

  # phpMyAdmin 数据库管理工具
  phpmyadmin:
    image: phpmyadmin:5.2
    container_name: pet-store-phpmyadmin
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - mysql
    networks:
      - pet-store-network

  # Nginx 反向代理 (为后续前端准备)
  nginx:
    image: nginx:1.25-alpine
    container_name: pet-store-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - wordpress
    networks:
      - pet-store-network

  # Nuxt.js 前端服务 (预留，暂时注释)
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: pet-store-frontend
  #   restart: unless-stopped
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - NODE_ENV=development
  #     - NUXT_PUBLIC_API_BASE=http://wordpress:80/wp-json
  #   volumes:
  #     - ./frontend:/app
  #     - /app/node_modules
  #   networks:
  #     - pet-store-network

volumes:
  wordpress_data:
  mysql_data:
  redis_data:

networks:
  pet-store-network:
    driver: bridge
